import { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import apiService from '../../services/apiService';
import EnhancedLoadingScreen from '../UI/EnhancedLoadingScreen';

const ResultOcean = () => {
  const { resultId } = useParams();
  const navigate = useNavigate();
  const [result, setResult] = useState(null);
  const [error, setError] = useState('');
  const fetchInProgressRef = useRef(false);
  const abortControllerRef = useRef(null);

  useEffect(() => {
    if (fetchInProgressRef.current) return;

    const fetchResult = async (retryCount = 0) => {
      const maxRetries = 5;
      const retryDelay = Math.min(1000 * Math.pow(2, retryCount), 10000);

      abortControllerRef.current = new AbortController();

      try {
        fetchInProgressRef.current = true;
        const response = await apiService.getResultById(resultId);

        if (!abortControllerRef.current?.signal.aborted) {
          if (response.success) {
            setResult(response.data);
            fetchInProgressRef.current = false;
          }
        }
      } catch (err) {
        if (abortControllerRef.current?.signal.aborted) return;

        if (err.response?.status === 404 && retryCount < maxRetries) {
          setTimeout(() => {
            if (!abortControllerRef.current?.signal.aborted) {
              fetchResult(retryCount + 1);
            }
          }, retryDelay);
        } else {
          const errorMessage = retryCount >= maxRetries
            ? `Result not found after ${maxRetries + 1} attempts. The analysis may still be processing.`
            : err.response?.data?.message || 'Failed to load results';
          setError(errorMessage);
          fetchInProgressRef.current = false;
        }
      }
    };

    if (resultId) {
      fetchResult();
    } else {
      navigate('/dashboard');
    }

    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      fetchInProgressRef.current = false;
    };
  }, [resultId, navigate]);

  // OCEAN trait definitions with minimalist design
  const oceanTraits = {
    openness: {
      name: 'Openness',
      subtitle: 'Creativity & Curiosity',
      icon: '🎨',
      description: 'Openness to experience emphasizes imagination and insight. It reflects your willingness to try new things, think creatively, and embrace novel ideas and experiences.',
      highTraits: ['Very creative', 'Open to trying new things', 'Focused on tackling new challenges', 'Happy to think about abstract concepts', 'Enjoys artistic experiences'],
      lowTraits: ['Dislikes change', 'Does not enjoy new things', 'Resists new ideas', 'Not very imaginative', 'Prefers routine and structure'],
      careerImplications: {
        high: 'Thrives in creative roles, research, innovation, and dynamic environments',
        low: 'Excels in structured roles, traditional industries, and routine-based work'
      }
    },
    conscientiousness: {
      name: 'Conscientiousness',
      subtitle: 'Organization & Discipline',
      icon: '📋',
      description: 'Conscientiousness is defined by high levels of thoughtfulness, good impulse control, and goal-directed behaviors. It reflects your tendency to be organized, responsible, and dependable.',
      highTraits: ['Spends time preparing', 'Finishes important tasks right away', 'Pays attention to detail', 'Enjoys having a set schedule', 'Strong work ethic'],
      lowTraits: ['Dislikes structure and schedules', 'Makes messes and doesn\'t take care of things', 'Procrastinates important tasks', 'Flexible and spontaneous', 'Comfortable with ambiguity'],
      careerImplications: {
        high: 'Suited for management, project coordination, and detail-oriented professions',
        low: 'Thrives in flexible, creative, and adaptive work environments'
      }
    },
    extraversion: {
      name: 'Extraversion',
      subtitle: 'Social Energy & Assertiveness',
      icon: '🗣️',
      description: 'Extraversion is characterized by excitability, sociability, talkativeness, and assertiveness. It indicates your preference for social interaction and drawing energy from external stimulation.',
      highTraits: ['Enjoys being the center of attention', 'Likes to start conversations', 'Enjoys meeting new people', 'Has a wide social circle', 'Feels energized when around other people'],
      lowTraits: ['Prefers solitude', 'Feels exhausted when having to socialize a lot', 'Finds it difficult to start conversations', 'Carefully thinks things through before speaking', 'Works well independently'],
      careerImplications: {
        high: 'Excels in sales, leadership, public speaking, and team-oriented roles',
        low: 'Thrives in research, writing, technical work, and independent roles'
      }
    },
    agreeableness: {
      name: 'Agreeableness',
      subtitle: 'Cooperation & Empathy',
      icon: '🤝',
      description: 'Agreeableness includes attributes such as trust, altruism, kindness, and affection. It reflects your tendency to be cooperative, trusting, and helpful in interactions with others.',
      highTraits: ['Has a great deal of interest in other people', 'Cares about others', 'Feels empathy and concern for other people', 'Enjoys helping others', 'Cooperative team player'],
      lowTraits: ['Takes little interest in others', 'Direct and honest communicator', 'Has little interest in other people\'s problems', 'Competitive nature', 'Independent thinker'],
      careerImplications: {
        high: 'Suited for counseling, healthcare, education, and service-oriented roles',
        low: 'Excels in competitive fields, negotiations, and leadership positions'
      }
    },
    neuroticism: {
      name: 'Neuroticism',
      subtitle: 'Emotional Sensitivity',
      icon: '🌊',
      description: 'Neuroticism is characterized by emotional instability and tendency to experience negative emotions. It measures your emotional stability and resilience to stress.',
      highTraits: ['Experiences a lot of stress', 'Worries about many different things', 'Gets upset easily', 'Experiences dramatic shifts in mood', 'Sensitive to criticism'],
      lowTraits: ['Emotionally stable', 'Deals well with stress', 'Rarely feels sad or depressed', 'Doesn\'t worry much', 'Resilient to setbacks'],
      careerImplications: {
        high: 'Benefits from supportive environments and stress management resources',
        low: 'Thrives in high-pressure, demanding, and unpredictable work environments'
      }
    }
  };

  const getScoreLevel = (score) => {
    if (score >= 4.5) return { level: 'Very High', intensity: 'text-gray-900 font-semibold' };
    if (score >= 3.5) return { level: 'High', intensity: 'text-gray-800 font-medium' };
    if (score >= 2.5) return { level: 'Moderate', intensity: 'text-gray-700 font-medium' };
    if (score >= 1.5) return { level: 'Low', intensity: 'text-gray-600 font-medium' };
    return { level: 'Very Low', intensity: 'text-gray-500 font-medium' };
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Loading State */}
        {!result && !error && (
          <EnhancedLoadingScreen
            title="Loading OCEAN Results..."
            subtitle="Analyzing your personality profile"
            skeletonCount={4}
            className="min-h-[600px]"
          />
        )}

        {/* Error State */}
        {error && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm"
          >
            <div className="flex items-center">
              <div className="text-gray-400 mr-3">⚠️</div>
              <div>
                <h3 className="text-gray-900 font-semibold">Unable to Load Results</h3>
                <p className="text-gray-600 text-sm mt-1">{error}</p>
                <div className="mt-4 space-x-3">
                  <button
                    onClick={() => window.location.reload()}
                    className="bg-gray-900 text-white px-4 py-2 rounded-md text-sm hover:bg-gray-800 transition-colors"
                  >
                    Retry
                  </button>
                  <button
                    onClick={() => navigate(`/results/${resultId}`)}
                    className="bg-gray-100 text-gray-700 px-4 py-2 rounded-md text-sm hover:bg-gray-200 transition-colors"
                  >
                    Back to Overview
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Main Content */}
        {result && (
          <>
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-8"
            >
              <div className="flex justify-between items-start mb-6">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">
                    OCEAN Personality Assessment
                  </h1>
                  <p className="text-gray-600 max-w-2xl">
                    The Big Five personality model reveals your core traits and behavioral tendencies across five key dimensions.
                  </p>
                </div>
                <div className="flex space-x-3">
                  <button
                    onClick={() => navigate(`/results/${resultId}`)}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                  >
                    ← Back
                  </button>
                  <button
                    onClick={() => navigate('/dashboard')}
                    className="px-4 py-2 bg-gray-900 text-white rounded-md hover:bg-gray-800 transition-colors"
                  >
                    Dashboard
                  </button>
                </div>
              </div>

              <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
                <div className="flex items-center justify-between text-sm text-gray-600">
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-gray-900 rounded-full mr-2"></span>
                    Completed: {formatDate(result.created_at)}
                  </div>
                  <span className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-xs font-medium">
                    Big Five Model
                  </span>
                </div>
              </div>
            </motion.div>

            {/* Main Grid Layout */}
            {result.assessment_data?.ocean && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {Object.entries(result.assessment_data.ocean).map(([traitKey, score], index) => {
                  const trait = oceanTraits[traitKey];
                  if (!trait) return null;

                  const scoreInfo = getScoreLevel(score);
                  const isHigh = score >= 3.5;
                  const relevantTraits = isHigh ? trait.highTraits : trait.lowTraits;

                  return (
                    <motion.div
                      key={traitKey}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-md transition-shadow"
                    >
                      {/* Header */}
                      <div className="bg-gray-50 border-b border-gray-200 p-6">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <span className="text-2xl mr-3">{trait.icon}</span>
                            <div>
                              <h3 className="text-xl font-bold text-gray-900">{trait.name}</h3>
                              <p className="text-sm text-gray-600">{trait.subtitle}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-2xl font-bold text-gray-900">{score.toFixed(1)}</div>
                            <div className={`text-sm ${scoreInfo.intensity}`}>{scoreInfo.level}</div>
                          </div>
                        </div>
                      </div>

                      {/* Content */}
                      <div className="p-6">
                        {/* Description */}
                        <p className="text-gray-700 mb-4 leading-relaxed">{trait.description}</p>

                        {/* Progress Bar */}
                        <div className="mb-6">
                          <div className="bg-gray-200 rounded-full h-2">
                            <motion.div
                              className="bg-gray-900 h-2 rounded-full"
                              initial={{ width: 0 }}
                              animate={{ width: `${(score / 5) * 100}%` }}
                              transition={{ duration: 1, delay: 0.5 + index * 0.1 }}
                            />
                          </div>
                        </div>

                        {/* Characteristics */}
                        <div className="mb-6">
                          <h4 className="font-semibold text-gray-900 mb-3">
                            {isHigh ? 'Your High Score Indicates:' : 'Your Low Score Indicates:'}
                          </h4>
                          <div className="space-y-2">
                            {relevantTraits.slice(0, 4).map((characteristic, idx) => (
                              <div key={idx} className="flex items-start text-sm text-gray-700">
                                <span className="text-gray-400 mr-2 mt-0.5">•</span>
                                {characteristic}
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Career Implications */}
                        <div className="bg-gray-50 rounded-lg p-4 border border-gray-100">
                          <h4 className="font-semibold text-gray-900 mb-2">Career Implications</h4>
                          <p className="text-sm text-gray-700">
                            {isHigh ? trait.careerImplications.high : trait.careerImplications.low}
                          </p>
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            )}

          </>
        )}
      </div>
    </div>
  );
};

export default ResultOcean;